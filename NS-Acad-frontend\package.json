{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^2.1.4", "@hookform/resolvers": "^3.6.0", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.6", "axios": "^1.7.9", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^16.4.7", "framer-motion": "^11.3.28", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.510.0", "pdfjs-dist": "^3.4.120", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.52.0", "react-icons": "^5.2.1", "react-pdf": "^6.2.2", "react-router-dom": "^6.28.1", "react-toastify": "^10.0.6", "styled-components": "^6.1.12", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.6", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.19", "daisyui": "^4.12.2", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "path": "^0.12.7", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^5.4.14"}}