{"name": "acad-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js"}, "dependencies": {"axios": "^1.7.9", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-session": "^1.18.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "mongoose": "^8.4.1", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.3", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0", "path": "^0.12.7", "react-router-dom": "^7.1.1", "react-toastify": "^11.0.2"}}